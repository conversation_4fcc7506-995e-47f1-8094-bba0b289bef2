
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Loader2, Phone } from "lucide-react";
import { callContacts, createContactForShortCall } from "@/app/api/contacts";
import PhoneInput from 'react-phone-input-2';
import { toast } from "sonner";
import { Agent } from '@/types/agent.types';
import 'react-phone-input-2/lib/style.css';
import { getRegionFromPhoneNumber } from '@/lib/phone-utils';

interface CallDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agent: Agent | null;
  
}


export function AgentPhoneCallDialog({ isOpen, onClose, agent }: CallDialogProps) {
  const [callFormData, setCallFormData] = useState({ name: '', phoneNumber: '' });
  const [isCallLoading, setIsCallLoading] = useState(false);
  const [callError, setCallError] = useState<string | null>(null);

  const handleStartCall = async () => {
    if (!agent) return;
    setIsCallLoading(true);
    setCallError(null);

    try {
       const cleanPhone = callFormData.phoneNumber.replace(/\s+/g, '');
       const phoneWithPrefix = cleanPhone.startsWith('+') ? cleanPhone : `+${cleanPhone}`;

      // Try to create contact first
      let contactExists = false;
      try {
        await createContactForShortCall({
          contactName: callFormData.name,
          phoneNumber: phoneWithPrefix,
          campaigns: [] // Empty campaigns array as discussed
        });
      } catch (error: any) {
        // If error is not "contact already exists", throw it
        if (error.message.includes('Conflict') || error.message.includes('already exists')) {
        contactExists = true;
        console.log('Contact already exists, proceeding with call');
      } else {
        // If it's a different error, throw it
        throw error;
      }
      }

        // Get region from phone number using utility function
      const region = getRegionFromPhoneNumber(phoneWithPrefix) || '';
  
      const contactsPayload = [{
        Name: callFormData.name,
        MobileNumber: phoneWithPrefix
      }];

      await callContacts(agent.id, contactsPayload, region);
      toast.success(
      contactExists 
        ? 'Call initiated with existing contact' 
        : 'Contact created and call initiated'
      );
      onClose();
    } catch (error: any) {
      console.error('Call error:', error);
      if (error.message.includes('already exists')) {
        setCallError('Contact already exists with this name and phone number');
      } else {
        setCallError(error instanceof Error ? error.message : 'Failed to initiate call');
      }
      toast.error('Failed to process request');
    } finally {
      setIsCallLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        setCallFormData({ name: '', phoneNumber: '' });
        setCallError(null);
      }
      onClose();
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Start Phone Call</DialogTitle>
          <DialogDescription>
            Enter your details to start a call with
            <span className="font-bold ml-1">
              {agent?.name}
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">Your Name</label>
            <Input
              id="name"
              placeholder="Enter your name"
              className="mt-2"
              value={callFormData.name}
              onChange={(e) => setCallFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="phone" className="text-sm font-medium">Phone Number</label>
            <PhoneInput
              country={'nl'} // Default country
              value={callFormData.phoneNumber}
              onChange={(phone: any) => setCallFormData(prev => ({ ...prev, phoneNumber: `+${phone}` }))}
              containerClass="mt-2"
              inputClass="!w-full !h-10 !pl-[48px] !rounded-md !border !border-input !bg-background !px-3 !py-2 !text-sm !ring-offset-background file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-muted-foreground focus-visible:!outline-none focus-visible:!ring-2 focus-visible:!ring-ring focus-visible:!ring-offset-2 disabled:!cursor-not-allowed disabled:!opacity-50"
              buttonClass="!border-r-0 !bg-transparent !border !border-input"
              dropdownClass="!bg-background !border !border-input"
              specialLabel=""
            />
          </div>

          {callError && (
            <div className="text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
              {callError}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            onClick={handleStartCall}
            disabled={isCallLoading || !callFormData.name || !callFormData.phoneNumber}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {isCallLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Initiating...
              </>
            ) : (
              <>
                <Phone className="mr-2 h-4 w-4" />
                Start Call
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}