import {
  Injectable,
  HttpException,
  HttpStatus,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from "axios";
import { Model } from "mongoose";
import * as qs from "qs";
import { ContactDocument } from "src/contacts/interfaces/contact.interface";
import { HistoryService } from "src/history/history.service";
import { LoggerService } from "src/logger/logger.service";
import { ScheduledCallService } from "src/scheduled-call/scheduled-call.service";
import moment from "moment-timezone";
import { toWords } from "number-to-words";
import { PhoneNumberUtil } from "google-libphonenumber";
import { CampaignDocument } from "src/campaign/interfaces/campaign.interface";
import { AgentService } from "src/agent/agent.service";
import { GlobalSettingsService } from "src/global-settings/global-settings.service";
import { UsersService } from "src/users/users.service";
import { OrganizationsService } from "src/organizations/organizations.service";
import { History } from "src/history/interfaces/history.interface";

@Injectable()
export class VapiService {
  private readonly VAPI_API_TOKEN = process.env.VAPI_API_TOKEN;
  private readonly PHONE_NUMBER_ID = process.env.PHONE_NUMBER_ID;
  private readonly plateform = process.env.PLATFORM_ENV || "dev";

  // Token cache
  private cachedAccessToken: string | null = null;
  private tokenFetchTime: number | null = null;

  // Track recently called contacts to prevent duplicates
  private recentlyCalledContacts = new Map<string, number>();
  private readonly CALL_TRACKING_EXPIRY = 30 * 1000; // 30 seconds

  constructor(
    private readonly historyService: HistoryService,
    private readonly loggerService: LoggerService,
    private readonly agentService: AgentService,
    @InjectModel("Contact")
    private readonly contactModel: Model<ContactDocument>,
    private readonly scheduledCallService: ScheduledCallService,
    @InjectModel("Campaign")
    private readonly campaignModel: Model<CampaignDocument>,
    @InjectModel("History")
    private readonly historyModel: Model<History>,
    private readonly globalSettingsService: GlobalSettingsService,
    private readonly usersService: UsersService,
    private readonly organizationsService: OrganizationsService
  ) {}

  async getToken(
    retryCount = 0,
    maxRetries = 5,
    forceRefresh = false
  ): Promise<string> {
    const baseDelay = 1000; // 1 second base delay
    const maxDelay = 32000; // 32 seconds max delay

    // If we have a cached token and it's not a forced refresh, return it
    if (!forceRefresh && this.cachedAccessToken) {
      await this.loggerService.log("Using cached Zoho access token");
      return this.cachedAccessToken;
    }

    try {
      await this.loggerService.log("Fetching new Zoho access token...");

      const data = qs.stringify({
        grant_type: "refresh_token",
        client_id:
          process.env.CLIENT_ID || "1000.S28BMCHCBV5VCRQXG6U9QNFG5KS03Q",
        client_secret:
          process.env.CLIENT_SECRET ||
          "11cf239866af251ae9405451664146ece6586fd74d",
        refresh_token:
          process.env.REFRESH_TOKEN ||
          "**********************************************************************",
      });

      const response = await axios.post(
        "https://accounts.zoho.com/oauth/v2/token",
        data,
        { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
      );

      // Cache the token and record the time
      this.cachedAccessToken = response.data.access_token;
      this.tokenFetchTime = Date.now();

      await this.loggerService.log(
        "Zoho access token fetched and cached successfully."
      );
      return this.cachedAccessToken;
    } catch (error) {
      await this.loggerService.error(
        "Error fetching Zoho access token",
        error.response?.data || error.message
      );

      // If we haven't exceeded max retries and error indicates rate limiting
      if (
        retryCount < maxRetries &&
        (error.response?.data?.error === "Access Denied" ||
          error.response?.status === 429)
      ) {
        // Calculate delay with exponential backoff: 2^retryCount * baseDelay
        const delay = Math.min(Math.pow(2, retryCount) * baseDelay, maxDelay);

        await this.loggerService.log(
          `Retrying token fetch after ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`
        );

        // Wait for the calculated delay
        await new Promise((resolve) => setTimeout(resolve, delay));

        // Retry the request with incremented retry count
        return this.getToken(retryCount + 1, maxRetries, true);
      }

      // If we've exhausted retries or it's a different error, throw
      throw new HttpException(
        "Error fetching token",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async callZohoApi(
    url: string,
    method: "get" | "post" | "put" | "delete" = "post",
    data?: any
  ): Promise<any> {
    try {
      // Get token (cached if available)
      const accessToken = await this.getToken();

      // Make the API call
      const config: AxiosRequestConfig = {
        headers: {
          Authorization: `Zoho-oauthtoken ${accessToken}`,
          "Content-Type": "application/json",
        },
      };

      let response: AxiosResponse;

      if (method === "get") {
        response = await axios.get(url, config);
      } else if (method === "put") {
        response = await axios.put(url, data, config);
      } else if (method === "delete") {
        response = await axios.delete(url, config);
      } else {
        // Default to POST
        response = await axios.post(url, data, config);
      }

      return response.data;
    } catch (error) {
      // Check if error is due to unauthorized (token expired)
      if (error.response?.status === 401) {
        await this.loggerService.log(
          "Zoho token expired, refreshing and retrying request"
        );

        // Force refresh the token
        const newToken = await this.getToken(0, 5, true);

        // Retry the request with the new token
        const config: AxiosRequestConfig = {
          headers: {
            Authorization: `Zoho-oauthtoken ${newToken}`,
            "Content-Type": "application/json",
          },
        };

        let response: AxiosResponse;

        if (method === "get") {
          response = await axios.get(url, config);
        } else if (method === "put") {
          response = await axios.put(url, data, config);
        } else if (method === "delete") {
          response = await axios.delete(url, config);
        } else {
          // Default to POST
          response = await axios.post(url, data, config);
        }

        return response.data;
      }

      // For other errors, just rethrow
      throw error;
    }
  }

  private cleanupRecentlyCalledContacts() {
    const now = Date.now();

    for (const [key, timestamp] of this.recentlyCalledContacts.entries()) {
      if (now - timestamp > this.CALL_TRACKING_EXPIRY) {
        this.recentlyCalledContacts.delete(key);
      }
    }
  }

  async callContacts(
    contacts: any[],
    agentId: string,
    region: string,
    userId?: string
  ): Promise<any[]> {
    if (!Array.isArray(contacts)) {
      await this.loggerService.error(
        "Invalid request: expected an array of contacts"
      );
      throw new HttpException(
        "Invalid request: expected an array of contacts",
        HttpStatus.BAD_REQUEST
      );
    }

    // Check credits before making any calls
    if (userId) {
      const hasSufficientCredits = await this.usersService.hasSufficientCredits(userId, 1);
      if (!hasSufficientCredits) {
        await this.loggerService.error(`Insufficient credits for user ${userId} to make calls`);
        throw new HttpException(
          "Insufficient credits. Please add funds to your account.",
          HttpStatus.PAYMENT_REQUIRED
        );
      }
    }

    // Clean up old entries from tracking map
    this.cleanupRecentlyCalledContacts();

    const callResults = [];
    await this.loggerService.log(
      `Starting calls for ${contacts.length} contacts.`
    );

    // Deduplicate contacts by name and phone number to prevent multiple calls to the same contact
    const uniqueContacts = [];
    const contactKeys = new Set();

    for (const contact of contacts) {
      const contactKey = `${contact.Name}:${contact.MobileNumber}`;

      // Skip if we've already called this contact recently
      if (this.recentlyCalledContacts.has(contactKey)) {
        await this.loggerService.log(
          `Skipping call to ${contact.Name} - already called recently (within last 30 seconds)`
        );
        callResults.push({
          contact: contact.Name,
          status: "Skipped",
          message: "Contact was called recently",
        });
        continue;
      }

      if (!contactKeys.has(contactKey)) {
        contactKeys.add(contactKey);
        uniqueContacts.push(contact);
      } else {
        await this.loggerService.log(
          `Skipping duplicate contact: ${contact.Name} (${contact.MobileNumber})`
        );
      }
    }

    await this.loggerService.log(
      `After deduplication: Processing ${uniqueContacts.length} unique contacts out of ${contacts.length} total`
    );

    for (const contact of uniqueContacts) {
      const contactKey = `${contact.Name}:${contact.MobileNumber}`;

      // Mark this contact as recently called BEFORE making the API call
      this.recentlyCalledContacts.set(contactKey, Date.now());

      const Existingcontact = await this.contactModel
        .findOne({
          contactName: contact.Name,
          phoneNumber: contact.MobileNumber,
        })
        .exec();

      if (!Existingcontact) {
        throw new NotFoundException(
          `Contact not found with name ${contact.Name} and phone ${contact.MobileNumber}`
        );
      }
      const totalPayableAmountWords =
        Existingcontact.totalPayableAmount !== null &&
        Existingcontact.totalPayableAmount !== undefined
          ? toWords(Existingcontact.totalPayableAmount)
          : null;
      const pendingPayableAmountWords =
        Existingcontact.pendingPayableAmount !== null &&
        Existingcontact.pendingPayableAmount !== undefined
          ? toWords(Existingcontact.pendingPayableAmount)
          : null;
      const lastPaymentAmountWords =
        Existingcontact.lastPaymentAmount !== null &&
        Existingcontact.lastPaymentAmount !== undefined
          ? toWords(Existingcontact.lastPaymentAmount)
          : null;
      const unitPriceWords =
        Existingcontact.unitPrice !== null &&
        Existingcontact.unitPrice !== undefined
          ? toWords(Existingcontact.unitPrice)
          : null;
      const paidAmtIncludingWords =
        Existingcontact.paidAmtIncluding !== null &&
        Existingcontact.paidAmtIncluding !== undefined
          ? toWords(Existingcontact.paidAmtIncluding)
          : null;
      const FormateddueDate = moment(Existingcontact.dueDate).format(
        "Do MMMM YYYY"
      );
      const currentTime = moment()
        .tz(Existingcontact.region)
        .format("YYYY-MM-DD HH:mm:ss");

      // Prepare the variable values for the assistant overrides
      const variableValues = {
        contactName: Existingcontact.contactName,
        phoneNumber: Existingcontact.phoneNumber,
        lastCall: Existingcontact.lastCall,
        region: Existingcontact.region,
        projectName: Existingcontact.projectName,
        unitNumber: Existingcontact.unitNumber,
        totalPayableAmount: totalPayableAmountWords,
        pendingPayableAmount: pendingPayableAmountWords,
        dueDate: FormateddueDate,
        totalInstallments: Existingcontact.totalInstallments,
        paymentType: Existingcontact.paymentType,
        pendingInstallments: Existingcontact.pendingInstallments,
        lastPaymentDate: Existingcontact.lastPaymentDate,
        lastPaymentAmount: lastPaymentAmountWords,
        lastPaymentType: Existingcontact.lastPaymentType,
        collectionBucket: Existingcontact.collectionBucket,
        currentTime: currentTime,
        unitPrice: unitPriceWords,
        paidAMTIncluding: paidAmtIncludingWords,
      };

      const agent = await this.agentService.findById(agentId);
      let phoneNumberId = "";

      const localPrefixes = (
        process.env.LOCAL_PHONE_NUMBER_CODE || "+971"
      ).split(",");

      const isLocalNumber = localPrefixes.some((prefix) =>
        contact.MobileNumber.startsWith(prefix.trim())
      );

      if (
        isLocalNumber &&
        agent.localPhoneNumberId !== null &&
        agent.localPhoneNumberId !== ""
      ) {
        phoneNumberId = agent.localPhoneNumberId;
      } else {
        phoneNumberId = agent.internationalPhoneNumberId;
      }

      const isValidEnglishName = (name: string): boolean => {
        const trimmed = name.trim();
        const pattern = /^[A-Za-z]+(?:[ '-][A-Za-z]+)*$/;
        return pattern.test(trimmed);
      };

      let callData: any = {
        type: "outboundPhoneCall",
        assistantId: agentId,
        phoneNumberId,
        customer: {
          number: contact.MobileNumber,
          name: contact.Name,
        },
        metadata: { contactName: contact.Name, region },
        assistantOverrides: {
          variableValues: variableValues,
          firstMessage: isValidEnglishName(contact.Name)
            ? `Hi, is this ${contact.Name}?`
            : "Hi there,",
        },
      };

      const currentDate = moment();
      const dueDate = moment(Existingcontact.dueDate);
      const isDueDateInFuture = dueDate.isAfter(currentDate);

      if (
        isDueDateInFuture &&
        agentId === "963d5cfe-d29f-4acb-844f-c4d5612c1f0c"
      ) {
        callData.assistantId = "bfbc0825-7e18-4538-820e-415a46c218e3";
      }

      if (
        isDueDateInFuture &&
        agentId === "963d5cfe-d29f-4acb-844f-c4d5612c1f0c" &&
        Existingcontact.paymentType === "PDC"
      ) {
        callData.assistantId = "57ec8c89-ea11-486c-84fa-45ec53600cae";
      }

      try {
        const response = await axios.post(
          "https://api.vapi.ai/call",
          callData,
          {
            headers: {
              Authorization: `Bearer ${this.VAPI_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );
        await this.loggerService.log(
          `Call initiated successfully for ${contact.Name}.`
        );
        callResults.push({
          contact: contact.Name,
          status: "Success",
          response: response.data,
        });
      } catch (error) {
        // If there's an error, remove the contact from the recently called list
        // so we can try again later
        this.recentlyCalledContacts.delete(contactKey);

        await this.loggerService.error(
          `Failed to initiate call for ${contact.Name}`,
          error.message
        );
        callResults.push({
          contact: contact.Name,
          status: "Failed",
          error: error.response?.data?.message || error.message,
        });
      }
    }
    return callResults;
  }

  async processWebhook(body: any): Promise<any> {
    try {
      await this.loggerService.log("Processing webhook event...");
      const message = body.message;
      const customer = message.customer || {};
      const analysis = message.analysis || {};
      const artifact = message.artifact || {};
      const structuredData = analysis.structuredData || {};
      let callEndReason = message.endedReason || null;

      const callDurationMs = message.durationMs || 0;

      if (
        callEndReason ===
        "call.in-progress.error-sip-telephony-provider-failed-to-connect-call"
      ) {
        callEndReason = "customer-out-of-reach";
      }
      const agentId =
        message.assistant?.id || message.call?.assistantId || null;

      await this.loggerService.log(
        `Extracted agentId from webhook payload: ${agentId}`
      );

      await this.loggerService.log(
        `Webhook message type: ${message.type || "unknown"}`
      );

      if (process.env.NODE_ENV === "development") {
        await this.loggerService.log(
          `Webhook payload structure: ${JSON.stringify({
            hasAssistant: !!message.assistant,
            hasAssistantId: !!message.assistant?.id,
            hasCall: !!message.call,
            hasCallAssistantId: !!message.call?.assistantId,
            messageType: message.type || "unknown",
          })}`
        );
      }

      if (!agentId) {
        await this.loggerService.log(
          `WARNING: No agentId found in webhook payload. This may cause issues with scheduled calls.`
        );
      }

      const emotions = structuredData.emotions || "Neutral";
      const callBackRequest = structuredData.callBackRequest || "N/A";
      const region = message.call?.metadata?.region || "UTC";

      if (!message.call?.metadata?.region) {
        await this.loggerService.log(
          `WARNING: No region found in webhook payload. Using default: UTC`
        );
      }

      const currentTime = moment().tz(region).format("YYYY-MM-DD HH:mm:ss");

      const payload = {
        fullName: customer.name || null,
        mobileNumber: customer.number || null,
        interest: structuredData.investment_type || null,
        timezone: region,
        callTranscript: artifact.transcript || null,
        callSummary: analysis.summary || message.summary || null,
        callStartTime:
          message.startedAt !== undefined
            ? message.startedAt
            : new Date().toISOString(),
        callEndTime: message.endedAt || null,
        callDuration: message.durationMs || null,
        callRoute: null,
        callPurpose: null,
        callEndReason,
        callCost: message.cost || null,
        bookedStatus: null,
        confirmedStatus: null,
        additionalQuestions: null,
        recordingUrl: message.recordingUrl || null,
        preferredProject: structuredData.preferred_project || null,
        preferredLocation: structuredData.preferred_location || null,
        preferredUnitType: structuredData.preferred_unit_type || null,
        projectType: structuredData.project_type || null,
        investmentType: structuredData.investment_type || null,
        budget: structuredData.budget || null,
        recentContact: true,
        emotions,

        brokenPromise: structuredData.brokenPromise || null,
        callBackLanguage: structuredData.callBackLanguage || null,
        callBackRequest: structuredData.callBackRequest || null,
        claimedPaidAwaitingPOP: structuredData.claimedPaidAwaitingPOP || null,
        doNotCall: structuredData.doNotCall || null,
        followingPaymentPlan: structuredData.followingPaymentPlan || null,
        fullyPaid: structuredData.fullyPaid || null,
        fullyPaidByPDC: structuredData.fullyPaidByPDC || null,
        incorrectContactDetails: structuredData.incorrectContactDetails || null,
        mortgage: structuredData.mortgage || null,
        notResponding: structuredData.notResponding || null,
        notRespondingSOASent: structuredData.notRespondingSOASent || null,
        notWillingToPay: structuredData.notWillingToPay || null,
        popRaised: structuredData.popRaised || null,
        promiseToPay: structuredData.promiseToPay || null,
        promiseToPayPartial: structuredData.promiseToPayPartial || null,
        refuseToPay: structuredData.refuseToPay || null,
        thirdParty: structuredData.thirdParty || null,
        willingToPay: structuredData.willingToPay || null,

        Response: structuredData.response || null,
        Notes: structuredData.notes || null,
        GuestRequest: structuredData.guestRequest || null,
        Channel: structuredData.channel || null,
      };
      const { name: agent } = await this.agentService.findById(agentId);

      await this.loggerService.log(`Saving call history for ${customer.name}`);
      await this.historyService.create({ ...payload, agent, emotions });
      await this.loggerService.log(
        `Call saved in history for ${customer.name}`
      );

      if (customer.name && customer.number) {
        try {
          const existingContact = await this.contactModel
            .findOne({
              contactName: customer.name,
              $or: [
                { phoneNumber: customer.number },
                { phoneNumber: this.normalizePhoneNumber(customer.number) },
                {
                  phoneNumber: {
                    $regex: new RegExp(
                      `^\\+${customer.number.replace(/^\+(\d+).*$/, "$1")}.*${customer.number.slice(-6)}$`
                    ),
                  },
                },
              ],
            })
            .exec();

          if (
            existingContact &&
            (existingContact.unansweredCalls === null ||
              existingContact.unansweredCalls === undefined)
          ) {
            existingContact.unansweredCalls = 0;
          }

          let campaignMaxRecalls = 3;
          let campaignRecallHours = 2;
          let callWindowStartTime = "09:00";
          let callWindowEndTime = "17:00";
          let callWindowDaysOfWeek = [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
          ];

          if (
            existingContact &&
            existingContact.campaigns &&
            existingContact.campaigns.length > 0
          ) {
            try {
              const campaignId = existingContact.campaigns[0];
              const campaign = await this.campaignModel
                .findById(campaignId)
                .exec();

              if (campaign) {
                const campaignData = campaign.toObject() as any;
                campaignMaxRecalls = campaignData.maxRecalls || 3;
                campaignRecallHours = campaignData.recallHours || 2;

                if (campaignData.callWindow) {
                  callWindowStartTime =
                    campaignData.callWindow.startTime || "09:00";
                  callWindowEndTime =
                    campaignData.callWindow.endTime || "17:00";
                  callWindowDaysOfWeek = campaignData.callWindow.daysOfWeek || [
                    "monday",
                    "tuesday",
                    "wednesday",
                    "thursday",
                    "friday",
                  ];
                }

                await this.loggerService.log(
                  `Using campaign settings: maxRecalls=${campaignMaxRecalls}, recallHours=${campaignRecallHours}, window=${callWindowStartTime}-${callWindowEndTime}`
                );
              }
            } catch (error) {
              await this.loggerService.error(
                `Error fetching campaign settings, using defaults`,
                error.message
              );
            }
          }

          if (
            (callEndReason === "voicemail" ||
              callEndReason === "customer-did-not-answer" ||
              callEndReason === "customer-busy" ||
              callEndReason === "customer-out-of-reach") &&
            existingContact &&
            existingContact.unansweredCalls < campaignMaxRecalls
          ) {
            await this.loggerService.log(
              `Scheduling a call for contact ${customer.name} in ${campaignRecallHours} hours, reason: ${callEndReason}`
            );
            const currentRegionTime = moment.tz(region);
            const recallTime = currentRegionTime
              .clone()
              .add(campaignRecallHours, "hour");
            let scheduledTime: moment.Moment;

            const [startHour, startMinute] = callWindowStartTime
              .split(":")
              .map(Number);
            const [endHour, endMinute] = callWindowEndTime
              .split(":")
              .map(Number);

            if (
              recallTime.hour() > endHour ||
              (recallTime.hour() === endHour && recallTime.minute() > endMinute)
            ) {
              scheduledTime = this.findNextAllowedDay(
                currentRegionTime,
                callWindowDaysOfWeek
              )
                .hour(startHour)
                .minute(startMinute)
                .second(0);

              await this.loggerService.log(
                `Scheduling call for next allowed day at ${callWindowStartTime} because recall time would be after ${callWindowEndTime}`
              );
            } else {
              scheduledTime = recallTime;
            }

            try {

              const assistantId =
                message.assistant?.id || message.call?.assistantId || agentId;

              await this.loggerService.log(
                `Using assistantId for scheduled call: ${assistantId}`
              );

              if (!assistantId) {
                throw new Error("No assistantId found in the webhook payload");
              }

              await this.loggerService.log(
                `Attempting to create scheduled call for ${customer.name} at ${scheduledTime.toISOString()} in region ${region} with assistantId: ${assistantId}`
              );

              await this.scheduledCallService.createScheduledCall({
                agentId: assistantId,
                contacts: [
                  {
                    Name: existingContact.contactName,
                    MobileNumber: existingContact.phoneNumber,
                  },
                ],
                scheduledByName: agent,
                scheduledTime: scheduledTime.toISOString(),
                region: region,
              });

              await this.loggerService.log(
                `Successfully created scheduled call for ${customer.name}`
              );

              existingContact.unansweredCalls += 1;
              await existingContact.save();
            } catch (error) {
              await this.loggerService.error(
                `Failed to create scheduled call for ${customer.name}`,
                error.message || "Unknown error"
              );
              // Continue processing even if scheduling fails
            }
          }

          if (existingContact && message.startedAt !== undefined) {
            try {
              existingContact.lastCall = new Date(message.startedAt);
              existingContact.unansweredCalls = 0;
              await existingContact.save();
              await this.loggerService.log(
                `Updated lastCall for contact ${customer.name} to ${message.startedAt}`
              );
            } catch (error) {
              await this.loggerService.error(
                `Error updating lastCall for contact ${customer.name}`,
                error.message || "Unknown error"
              );
            }
          } else {
            await this.loggerService.log(
              `No matching contact found for ${customer.name} with number ${customer.number}`
            );
          }

          if (callBackRequest !== "N/A") {
            await this.loggerService.log(
              `Scheduling a call for contact ${customer.name} based on forwarded call instruction: ${callBackRequest}`
            );
            let nextCallTime = await this.calculateNextCallTime(
              currentTime,
              callBackRequest
            );

            // Check if the next call time is within the campaign's allowed calling time and day
            let nextCallMoment = moment(nextCallTime).tz(region);

            // Get campaign settings for the contact
            let campaignCallWindow = {
              startTime: callWindowStartTime,
              endTime: callWindowEndTime,
              daysOfWeek: callWindowDaysOfWeek,
            };

            // Parse call window times
            const [startHour, startMinute] = campaignCallWindow.startTime
              .split(":")
              .map(Number);
            const [endHour, endMinute] = campaignCallWindow.endTime
              .split(":")
              .map(Number);

            // Check if the next call time is outside the allowed time window
            const isOutsideTimeWindow =
              nextCallMoment.hour() < startHour ||
              (nextCallMoment.hour() === startHour &&
                nextCallMoment.minute() < startMinute) ||
              nextCallMoment.hour() > endHour ||
              (nextCallMoment.hour() === endHour &&
                nextCallMoment.minute() > endMinute);

            // Check if the day is allowed
            const dayName = nextCallMoment.format("dddd").toLowerCase();
            const normalizedDaysOfWeek = campaignCallWindow.daysOfWeek.map(
              (day) => day.toLowerCase()
            );
            const isDayAllowed = normalizedDaysOfWeek.includes(dayName);

            // If outside time window or day not allowed, reschedule to next allowed day
            if (isOutsideTimeWindow || !isDayAllowed) {
              await this.loggerService.log(
                `Requested call time ${nextCallTime} is outside campaign window or on non-allowed day. Rescheduling to next allowed day.`
              );

              // If current day is not allowed, find next allowed day
              let scheduledDay: moment.Moment;
              if (!isDayAllowed) {
                scheduledDay = this.findNextAllowedDay(
                  nextCallMoment,
                  campaignCallWindow.daysOfWeek
                );
              } else {
                // If just outside time window but day is allowed
                if (
                  nextCallMoment.hour() > endHour ||
                  (nextCallMoment.hour() === endHour &&
                    nextCallMoment.minute() > endMinute)
                ) {
                  // If after end time, go to next day
                  scheduledDay = this.findNextAllowedDay(
                    nextCallMoment,
                    campaignCallWindow.daysOfWeek
                  );
                } else {
                  // If before start time but on allowed day, use same day
                  scheduledDay = nextCallMoment.clone();
                }
              }

              // Set to start time of the allowed day
              scheduledDay
                .hour(startHour)
                .minute(startMinute)
                .second(0)
                .millisecond(0);
              nextCallTime = scheduledDay.toISOString();

              await this.loggerService.log(
                `Rescheduled call to ${nextCallTime} (next available time in campaign window)`
              );
            }

            try {
              const assistantId =
                message.assistant?.id || message.call?.assistantId || agentId;

              await this.loggerService.log(
                `Using assistantId for callback scheduled call: ${assistantId}`
              );

              if (!assistantId) {
                throw new Error(
                  "No assistantId found in the webhook payload for callback"
                );
              }

              await this.loggerService.log(
                `Attempting to create callback scheduled call for ${existingContact.contactName} at ${nextCallTime} in region ${region} with assistantId: ${assistantId}`
              );

              await this.scheduledCallService.createScheduledCall({
                agentId: assistantId,
                contacts: [
                  {
                    Name: existingContact.contactName,
                    MobileNumber: existingContact.phoneNumber,
                  },
                ],
                scheduledByName: "agent",
                scheduledTime: nextCallTime,
                region: region,
              });

              await this.loggerService.log(
                `Successfully created callback scheduled call for ${existingContact.contactName}`
              );
            } catch (error) {
              await this.loggerService.error(
                `Failed to create callback scheduled call for ${existingContact.contactName}`,
                error.message || "Unknown error"
              );
              // Continue processing even if scheduling fails
            }
          }
          if (this.plateform === "binghatti") {
            this.triggerCallback({
              ...payload,
              previousCallTime: message.startedAt || null,
              existingContact,
            }).catch((err) => {
              this.loggerService.error(
                "Error in asynchronous callback",
                err.message
              );
            });
          }
        } catch (error) {
          await this.loggerService.error(
            `Error updating lastCall for contact ${customer.name}`,
            error.message
          );
        }
      }

      // Calculate and deduct call cost if this is an end-of-call report
      if (callDurationMs > 0) {
        console.log("🚀 ~ VapiService ~ processWebhook ~ callDurationMs:", callDurationMs)
        try {
          await this.loggerService.log(`Call duration: ${callDurationMs}ms`);

          // Convert duration from milliseconds to minutes
          const durationMinutes = callDurationMs / 60000;

          await this.loggerService.log(`Call duration in minutes: ${durationMinutes.toFixed(2)}`);

          // Update the history record with the calculated cost
          try {
            // Find the history record by customer name and number
            const historyRecord = await this.historyModel.findOne({
              fullName: customer.name,
              mobileNumber: customer.number,
              callStartTime: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Within the last 24 hours
            }).sort({ callStartTime: -1 }).exec(); // Get the most recent one

            if (historyRecord) {
              await this.loggerService.log(`Found history record for ${customer.name}, will update cost after calculating`);
            } else {
              await this.loggerService.log(`Could not find history record to update cost for ${customer.name}`);
            }
          } catch (error) {
            await this.loggerService.log(`Error finding history record: ${error.message}`);
          }

          // Find the contact to get the organization and calculate cost
          if (customer.name && customer.number) {
            // Find the contact by phone number
           const contact = await this.contactModel
            .findOne({
              contactName: customer.name,
              $or: [
                { phoneNumber: customer.number },
                { phoneNumber: this.normalizePhoneNumber(customer.number) },
                {
                  phoneNumber: {
                    $regex: new RegExp(
                      `^\\+${customer.number.replace(/^\+(\d+).*$/, "$1")}.*${customer.number.slice(-6)}$`
                    ),
                  },
                },
              ],
            })
            .exec();

            if (contact) {
              try {
                const campaign = await this.campaignModel.findOne({
                  'contacts.phoneNumber': contact.phoneNumber
                }).exec();

                if (campaign && campaign.createdBy) {
                  try {
                    // Find the user by name since campaign.createdBy contains the user's name, not ID
                    const user = await this.usersService.findByName(campaign.createdBy);
                    if (user && user._id && user.organizationId) {
                      // Get organization settings for call pricing
                      const organization = await this.organizationsService.findOne(user.organizationId.toString());
                      const pricePerMinute = organization.callPricePerMinute || 0.5; // Default to $0.5 per minute

                      // Calculate the cost (round up to 2 decimal places)
                      const callCost = Math.round((durationMinutes * pricePerMinute) * 100) / 100;

                      await this.loggerService.log(`Call cost calculated: $${callCost} (${durationMinutes.toFixed(2)} minutes at $${pricePerMinute}/minute)`);

                      // Update the history record with the calculated cost
                      try {
                        const historyRecord = await this.historyModel.findOne({
                          fullName: customer.name,
                          mobileNumber: customer.number,
                          callStartTime: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
                        }).sort({ callStartTime: -1 }).exec();

                        if (historyRecord) {
                          historyRecord.callCost = callCost.toString();
                          await historyRecord.save();
                          await this.loggerService.log(`Updated history record with calculated cost: $${callCost}`);
                        }
                      } catch (historyError) {
                        await this.loggerService.log(`Error updating history record with cost: ${historyError.message}`);
                      }

                      // Deduct the cost from the organization's credits using the found user ID
                      await this.usersService.deductCredits(user._id.toString(), callCost);
                      await this.loggerService.log(`Deducted $${callCost} from organization credits for user ${user._id} (${campaign.createdBy})`);
                    } else {
                      await this.loggerService.log(`User ${campaign.createdBy} not found or has no organization - cannot calculate call cost`);
                    }
                  } catch (userError) {
                    await this.loggerService.error(`Error finding user by name: ${userError.message}`);
                  }
                } else {
                  await this.loggerService.log(`No campaign found for contact - cannot calculate call cost`);
                }
              } catch (error) {
                await this.loggerService.error(`Error finding campaign for contact: ${error.message}`);
              }
            } else {
              await this.loggerService.log(`No contact found for ${customer.name} with number ${customer.number} - cannot calculate call cost`);
            }
          } else {
            await this.loggerService.log(`No customer phone number found - cannot calculate call cost`);
          }
        } catch (costError) {
          await this.loggerService.error("Error calculating or deducting call cost", costError.message);
          // Continue processing even if cost calculation fails
        }
      }

      const responsePayload = { message: "Webhook processed" };
      return responsePayload;
    } catch (error) {
      await this.loggerService.error("Error processing webhook", error.message);
      throw new HttpException(
        "Failed to process webhook",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private findNextAllowedDay(
    currentDate: moment.Moment,
    allowedDays: string[]
  ): moment.Moment {
    const normalizedAllowedDays = allowedDays.map((day) => day.toLowerCase());

    let nextDate = currentDate.clone().add(1, "day");

    for (let i = 0; i < 7; i++) {
      const dayName = nextDate.format("dddd").toLowerCase();
      if (normalizedAllowedDays.includes(dayName)) {
        return nextDate;
      }
      nextDate.add(1, "day");
    }

    return currentDate.clone().add(1, "day");
  }

  private async calculateNextCallTime(
    currentTime: string,
    forwardedCall: string
  ): Promise<string> {
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const prompt = `Given that the current time is "${currentTime}"", and the callback instruction is "${forwardedCall}", provide the exact next call time as an ISO formatted string. For example, if current time is "2025-04-03 08:01:00" and the instruction is "tomorrow morning at 9 AM", the output should be "2025-04-04T09:00:00.000". If the instruction is "after 2 hours", output the ISO string for two hours later. Only output the ISO datetime.`;

    // Define the function schema for structured output
    const functions = [
      {
        name: "set_next_call_time",
        description: "Set the next call time in ISO 8601 format",
        parameters: {
          type: "object",
          properties: {
            nextCallTime: {
              type: "string",
              description:
                "Next call time in ISO 8601 format (e.g., '2025-04-04T09:00:00.000')",
            },
          },
          required: ["nextCallTime"],
        },
      },
    ];

    try {
      const response = await axios.post(
        "https://api.openai.com/v1/chat/completions",
        {
          model: "gpt-3.5-turbo",
          messages: [
            {
              role: "user",
              content: prompt,
            },
          ],
          functions: functions,
          function_call: { name: "set_next_call_time" },
          max_tokens: 50,
          temperature: 0,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${openaiApiKey}`,
          },
        }
      );

      let nextCallTime: string;

      // If the API response includes a function call, parse its arguments
      if (response.data.choices[0].message.function_call) {
        const args = response.data.choices[0].message.function_call.arguments;
        const parsedArgs = JSON.parse(args);
        nextCallTime = parsedArgs.nextCallTime;
      } else {
        // Fallback: trim the raw message content
        nextCallTime = response.data.choices[0].message.content.trim();
      }

      return nextCallTime;
    } catch (error) {
      console.error("Error calculating next call time:", error);
      await this.loggerService.error("Error calculating next call time", error);
      return moment(currentTime).toISOString();
    }
  }

  private async triggerCallback(originalPayload: any): Promise<void> {
    try {
      const defaultLeadApiUrl =
        process.env.LEAD_API_URL || "https://www.zohoapis.com/crm/v7/ZLeads";

      const formatDate = (date: Date | string): string => {
        if (!date) return "";
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, "0");
        const day = String(d.getDate()).padStart(2, "0");
        const hours = String(d.getHours()).padStart(2, "0");
        const minutes = String(d.getMinutes()).padStart(2, "0");
        const seconds = String(d.getSeconds()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      let campaignName = "";
      let targetUrl = defaultLeadApiUrl;

      if (
        originalPayload.existingContact &&
        originalPayload.existingContact.campaigns &&
        originalPayload.existingContact.campaigns.length > 0
      ) {
        const campaignId = originalPayload.existingContact.campaigns[0];
        const campaignDoc = await this.campaignModel
          .findById(campaignId)
          .exec();
        if (campaignDoc && campaignDoc.name) {
          campaignName = campaignDoc.name;
        }
      }

      let zohoPayload: any;

      if (campaignName === "AquaRiseEvent") {
        targetUrl = "https://www.zohoapis.com/crm/v7/AICampaigns";

        zohoPayload = {
          data: [
            {
              Name: originalPayload.fullName || "",
              Response: originalPayload.Response || "N/A",
              Campaign_Notes: originalPayload.Notes || "N/A",
              Guest_Request: originalPayload.GuestRequest || "N/A",
              Channel: originalPayload.Channel || "N/A",
              Status: originalPayload.callEndReason || "N/A",
              Campaign_End_Call_Reason: originalPayload.callEndReason || "N/A",
              Event_Date: originalPayload.existingContact?.eventDate || "",
              Event_Location:
                originalPayload.existingContact?.eventLocation || "",
            },
          ],
        };

        await this.loggerService.log(
          `Sending AquaRiseEvent data to Zoho CRM: ${JSON.stringify(zohoPayload)}`
        );
      } else if (campaignName === "Sales") {
        // For Sales: limited fields and different target URL.
        targetUrl = "https://www.zohoapis.com/crm/v7/ZLeads";
        zohoPayload = {
          data: [
            {
              Name: originalPayload.fullName,
              Mobile: originalPayload.mobileNumber || "N/A",
              Region: originalPayload.timezone
                ? originalPayload.timezone
                : "N/A",
              Recent_Contact: originalPayload.recentContact ? "Yes" : "No",
              Previous_Call_Time: originalPayload.previousCallTime || "N/A",
              Campaign_Name: campaignName,
              Call_Sentiment: originalPayload.emotions || "N/A",
              Call_Status: originalPayload.callEndReason || "N/A",
              End_Call_Reason: originalPayload.callEndReason || "N/A",
              Interest: originalPayload.interest || "N/A",
              Call_Transcript: originalPayload.callTranscript || "N/A",
              Call_Summary: originalPayload.callSummary || "N/A",
              Call_Start_Time:
                formatDate(originalPayload.callStartTime) || "N/A",
              Call_End_Time: formatDate(originalPayload.callEndTime) || "N/A",
              Call_Duration: originalPayload.callDuration
                ? originalPayload.callDuration.toString()
                : "N/A",
              Call_Route: originalPayload.callRoute || "N/A",
              Call_Purpose: originalPayload.callPurpose || "N/A",
              Call_Cost: originalPayload.callCost
                ? originalPayload.callCost.toString()
                : "N/A",
              Recording_Url: originalPayload.recordingUrl || "N/A",
              AI_ProjectName: originalPayload.preferredProject || "N/A",
              AI_LocationName: originalPayload.preferredLocation || "N/A",
              AI_UnitType: originalPayload.preferredUnitType || "N/A",
              AI_ProjectType: originalPayload.projectType || "N/A",
              AI_InvestmentType: originalPayload.investmentType || "N/A",
              AI_Budget: originalPayload.budget || "N/A",
              Booked_Status: originalPayload.bookedStatus || "N/A",
              Confirmed_Status: originalPayload.confirmedStatus || "N/A",
              Additional_Questions:
                originalPayload.additionalQuestions || "N/A",
              Call_back_Language: originalPayload.callBackLanguage || "N/A",
              Call_Back_Request: originalPayload.callBackRequest || "N/A",
            },
          ],
          trigger: ["workflow"],
        };
      } else if (campaignName === "Collections") {
        // For Collections: include additional fields and send to a different URL.
        targetUrl = "https://www.zohoapis.com/crm/v7/Collections";
        zohoPayload = {
          data: [
            {
              Name: originalPayload.existingContact?.projectName || "",
              name1: originalPayload.fullName,
              Phone_Number: originalPayload.mobileNumber || "",
              Region: originalPayload.timezone ? originalPayload.timezone : "",
              Unit_Number: originalPayload.existingContact?.unitNumber || "",
              Total_Payable_Amount:
                originalPayload.existingContact?.totalPayableAmount?.toString() ||
                "",
              Pending_Payable_Amount:
                originalPayload.existingContact?.pendingPayableAmount?.toString() ||
                "",
              Due_Date:
                formatDate(originalPayload.existingContact?.dueDate) || "",
              Total_Number_of_Installments:
                originalPayload.existingContact?.totalInstallments?.toString() ||
                "",
              Payment_Type: originalPayload.existingContact?.paymentType || "",
              Pending_Installments:
                originalPayload.existingContact?.pendingInstallments?.toString() ||
                "",
              Last_Payment_Date: formatDate(
                originalPayload.existingContact?.lastPaymentDate
              ),
              Last_Payment_Amount:
                originalPayload.existingContact?.lastPaymentAmount?.toString() ||
                "",
              Last_Payment_Type:
                originalPayload.existingContact?.lastPaymentType || "",
              Collection_Bucket:
                originalPayload.existingContact?.collectionBucket || "",
              Unit_Price:
                originalPayload.existingContact?.unitPrice?.toString() || "",
              Paid_AMT_Including:
                originalPayload.existingContact?.paidAmtIncluding?.toString() ||
                "",
              Recent_Contact: originalPayload.recentContact ? "Yes" : "No",
              Previous_Call_Time: originalPayload.previousCallTime || "N/A",
              Campaign_Name: campaignName,
              Call_Sentiment: originalPayload.emotions || "N/A",
              Call_Status: originalPayload.callEndReason || "N/A",
              End_Call_Reason: originalPayload.callEndReason || "N/A",
              Interest: originalPayload.interest || "N/A",
              Call_Transcript: originalPayload.callTranscript || "N/A",
              Call_Summary: originalPayload.callSummary || "N/A",
              Call_Start_Time:
                formatDate(originalPayload.callStartTime) || "N/A",
              Call_End_Time: formatDate(originalPayload.callEndTime) || "N/A",
              Call_Duration: originalPayload.callDuration
                ? originalPayload.callDuration.toString()
                : "N/A",
              Call_Route: originalPayload.callRoute || "N/A",
              Call_Purpose: originalPayload.callPurpose || "N/A",
              Call_Cost: originalPayload.callCost
                ? originalPayload.callCost.toString()
                : "N/A",
              Recording_Url: originalPayload.recordingUrl || "N/A",
              Broken_Promise: originalPayload.brokenPromise || "N/A",
              Call_back_language: originalPayload.callBackLanguage || "N/A",
              Call_Back_Request: originalPayload.callBackRequest || "N/A",
              Claimed_paid_Awaiting_POP:
                originalPayload.claimedPaidAwaitingPOP || "N/A",
              Do_Not_Call: originalPayload.doNotCall || "N/A",
              Following_Payment_Plan:
                originalPayload.followingPaymentPlan || "N/A",
              Fully_Paid: originalPayload.fullyPaid || "N/A",
              Fully_Paid_by_PDC: originalPayload.fullyPaidByPDC || "N/A",
              Incorrect_contact_details:
                originalPayload.incorrectContactDetails || "N/A",
              Mortgage: originalPayload.mortgage || "N/A",
              Not_Responding: originalPayload.notResponding || "N/A",
              Not_Responding_SOA_Sent:
                originalPayload.notRespondingSOASent || "N/A",
              Not_willing_to_Pay: originalPayload.notWillingToPay || "N/A",
              POP_Raised: originalPayload.popRaised || "N/A",
              Promise_to_Pay: originalPayload.promiseToPay || "N/A",
              Promise_to_Pay_Partial:
                originalPayload.promiseToPayPartial || "N/A",
              Refuse_to_Pay: originalPayload.refuseToPay || "N/A",
              Third_Party: originalPayload.thirdParty || "N/A",
              Willing_to_pay: originalPayload.willingToPay || "N/A",
            },
          ],
          trigger: ["workflow"],
        };
      }

      const customerId = originalPayload.existingContact?.customerId || null;

      try {
        if (customerId) {
          await this.loggerService.log(
            `Updating lead data to: ${targetUrl}/${customerId}`
          );
          await this.callZohoApi(
            `${targetUrl}/${customerId}`,
            "put",
            zohoPayload
          );
        }

        await this.loggerService.log("Lead data sent successfully.");
      } catch (error) {
        await this.loggerService.error(
          "Error in Zoho API call",
          error.response?.data || error.message
        );
        throw error; // Re-throw to be caught by outer catch block
      }
    } catch (error: any) {
      await this.loggerService.error(
        "Error sending lead data",
        error.response?.data || error.message
      );
    }
  }

  private normalizePhoneNumber(phone: string): string {
    if (!phone) return phone;

    let normalized = phone.startsWith("+") ? phone : `+${phone}`;

    const uaeDoubleZeroPattern = /^\+971(0)(\d+)$/;
    if (uaeDoubleZeroPattern.test(normalized)) {
      normalized = normalized.replace(uaeDoubleZeroPattern, "+971$2");
    }

    return normalized;
  }
}
