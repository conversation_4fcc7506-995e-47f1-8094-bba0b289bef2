
"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>ertCircle, ArrowLeft, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

import AgentProfileTab from "@/components/agentscomponents/AgentProfileTab";
import AgentRoleTab from "@/components/agentscomponents/AgentRoleTab";
import AgentActionsTab from "@/components/agentscomponents/AgentActionsTab";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { useAuth } from "@/hooks/useAuth";
import { useAgent } from "@/hooks/useAgent";
import AgentVoiceTab from "@/components/agentscomponents/AgentVoiceTab";
import AgentBrainTab from "@/components/agentscomponents/AgentBrainTab";
import AgentAdvancedTab from "@/components/agentscomponents/AgentAdvancedTab";
import AgentS<PERSON>bar from "@/components/agentscomponents/AgentSidebar";


export const agentTabs = [
  { 
    value: 'profile',
    label: 'Profile',
    component: AgentProfileTab
  },
  {
    value: 'prompt',
    label: 'Prompt',
    component: AgentRoleTab
  },
  {
    value: 'voice',
    label: 'Voice',
    component: AgentVoiceTab
  },
  {
    value: 'brain',
    label: 'Brain',
    component: AgentBrainTab
  },
  {
    value: 'actions',
    label: 'Actions',
    component: AgentActionsTab
  },
  {
    value: 'advanced',
    label: 'Advanced',
    component: AgentAdvancedTab
  }
] as const;

interface EditAgentContentProps {
  agentId?: string;
}

export default function EditAgentContent({ agentId: propAgentId }: EditAgentContentProps) {
  const router = useRouter();
  const params = useParams();

  const [activeTab, setActiveTab] = useState("profile");
  const agentId = propAgentId || (params.id as string);

   // New state for saving and dialog
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [saveError, setShowError] = useState<string | null>(null);

  const { userRole } = useAuth();
  const {agent, setAgent, phoneNumbers, agentIsLoading, updateAgentMutation } = useAgent(agentId);

   
  useEffect(() => {
    if (!agentId) {
      router.push("/agents");
    }
  }, [agentId, router]);


 const handleSaveChanges = async () => {
    if (!agent) return;
    setIsSaving(true);
    setShowError(null);

    try {
      await updateAgentMutation.mutateAsync(agent);
      setShowSuccessDialog(true);
    } catch (error) {
      console.error("Error updating agent:", error);
      setShowError(error instanceof Error ? error.message : "Failed to update agent");
    } finally {
      setIsSaving(false);
    }
  };


  // Update the status toggle function
  const handleToggleStatus = async () => {
    if (!agent) return;

    const newStatus = agent.status === 'active' ? 'inactive' : 'active';

    try {
      await updateAgentMutation.mutateAsync({
        ...agent,
        status: newStatus
      });
    } catch (error) {
      console.error("Error updating agent status:", error);
      setShowError(error instanceof Error ? error.message : "Failed to update agent status");
    }
  };


  return (
    <>

    <div className="min-h-screen bg-gray-50/50 dark:bg-gray-900/50 ">

     {/* Header - Now outside and above everything */}
    <div className="w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center px-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/agents")}
          className="mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft className="h-5 w-5" />
          <span className="sr-only">Back</span>
        </Button>

        <h1 className="text-2xl font-bold tracking-tight">{agent?.name}</h1>
      </div>
    </div>


      {/* Main Content  Area*/}
    <div className="flex">
      {/* Sidebar */}
      <AgentSidebar 
        currentAgentId={agentId}
        userRole={userRole} 
      />

      {/* Main Content */}
      <div className="flex-1 min-h-[calc(100vh-4rem)]">
      {agentIsLoading ? (
          <div className="container py-4 px-4">
            <div className="animate-pulse">
              <div className="h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="mt-8 space-y-4">
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          </div>
        ) : agent && (
        <div className="p-6 max-w-5xl ">
          {/* Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <div className="border-b">
              <TabsList className="w-full justify-start h-auto bg-transparent p-0">
                    {agentTabs.map(tab => (
                      <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-3 bg-transparent"
                      >
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
            </div>

            <div className="bg-card rounded-lg border shadow-sm">
                  {agentTabs.map(tab => (
                    <TabsContent 
                      key={tab.value}
                      value={tab.value} 
                      className="m-0 focus-visible:outline-none focus-visible:ring-0"
                    >
                      <tab.component 
                        agent={agent} 
                        setAgent={setAgent} 
                        phoneNumbers={phoneNumbers}
                      />
                    </TabsContent>
                  ))}
                </div>
          </Tabs>
          {/* Save Changes Button */}
          <div className="mt-6 flex justify-end">
            <Button
              onClick={handleSaveChanges}
              disabled={isSaving}
              className="bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600"
            >
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </div>



          {/* Status indicator */}
        {userRole === "superadmin" && (
              <div className="mt-8 flex items-center justify-between py-4 px-6 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <div className={`h-3 w-3 rounded-full ${agent.status === 'active' ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                <span className="text-sm">
                  {agent.status === 'active' ? 'Active' : 'Inactive'}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleStatus}
              >
                {agent.status === 'active' ? 'Deactivate' : 'Activate'}
              </Button>
              </div>
        )}


          {/* Success Dialog */}
        <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Agent Updated Successfully
              </DialogTitle>
            </DialogHeader>
            <p className="text-sm text-muted-foreground">
              Your changes to {agent?.name} have been saved.
            </p>
            <DialogFooter className="flex gap-2 sm:justify-start">
              <Button
                variant="outline"
                onClick={() => setShowSuccessDialog(false)}
              >
                Continue Editing
              </Button>
              <Button
                onClick={() => router.push("/agents")}
                className="bg-black text-white dark:text-black dark:bg-white"
              >
                Back to Agents
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Error Dialog */}
        {saveError && (
          <Dialog open={!!saveError} onOpenChange={() => setShowError(null)}>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-red-500">
                  <AlertCircle className="h-5 w-5" />
                  Error Updating Agent
                </DialogTitle>
              </DialogHeader>
              <p className="text-sm text-muted-foreground">
                {saveError}
              </p>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowError(null)}
                >
                  Close
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
        </div>
 )}
  </div>
    </div>

    </div>

    </>
  );
}