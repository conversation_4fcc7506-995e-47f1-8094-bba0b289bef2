
export interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  status: "active" | "inactive";
  type: "text" | "voice" | "both";
  messagesHandled: number;
  localPhoneNumberId: string;
  internationalPhoneNumberId: string;
  firstMessage: string;
  voicemailMessage: string;
  backgroundDenoisingEnabled: boolean;
  avatar?: string;
  voice: {
    model: string;
    voiceId: string;
    provider: string;
    stability: number;
  };
  transcriber?: {
    provider: string;
    language: string;
    model: string;
    confidenceThreshold: number;
  };
  model: {
    model: string;
    provider: string;
    maxTokens: number;
    temperature: number;
    messages?: Array<{ role: string; content: string }>;
  };
  server: {
    url: string;
  };
  recordingEnabled?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface PhoneNumber {
  id: string;
  number: string;
  name?: string;
  provider?: string;
  status?: string;
}

export interface AgentQueryData {
  agent: Agent;
  phoneNumbers: PhoneNumber[];
}

export interface AgentTabProps {
  agent: Agent;
  setAgent: (agent: Agent) => void;
  phoneNumbers: PhoneNumber[];
  
}


export interface AgentSidebarProps {
  currentAgentId: string | null;
  userRole: string | null; 
  isLoading?: boolean;
}
