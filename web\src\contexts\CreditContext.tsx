/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { getUserCredits } from "@/app/api/users";
import { useWebSocket } from "@/hooks/useWebSocket";
import { toast } from "sonner";

interface CreditContextType {
  // Legacy fields for backward compatibility
  credits: number;
  minutes: number;
  // New monthly credits fields
  freeCreditsRemaining: number;
  paidCredits: number;
  totalAvailable: number;
  usingFreeCredits: boolean;
  freeMinutesRemaining: number;
  paidMinutes: number;
  totalMinutesAvailable: number;
  // Common fields
  callPricePerMinute: number;
  monthlyAllowance: number;
  hasSufficientCredits: boolean;
  isLoading: boolean;
  refreshCredits: () => Promise<void>;
  creditThreshold: number;
  organizationCreditThreshold: number;
  effectiveThreshold: number;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

interface CreditProviderProps {
  children: ReactNode;
  creditThreshold?: number;
}

export function CreditProvider({
  children,
  creditThreshold = 1
}: CreditProviderProps) {
  // Legacy fields for backward compatibility
  const [credits, setCredits] = useState<number>(0);
  const [minutes, setMinutes] = useState<number>(0);
  // New monthly credits fields
  const [freeCreditsRemaining, setFreeCreditsRemaining] = useState<number>(0);
  const [paidCredits, setPaidCredits] = useState<number>(0);
  const [totalAvailable, setTotalAvailable] = useState<number>(0);
  const [usingFreeCredits, setUsingFreeCredits] = useState<boolean>(false);
  const [freeMinutesRemaining, setFreeMinutesRemaining] = useState<number>(0);
  const [paidMinutes, setPaidMinutes] = useState<number>(0);
  const [totalMinutesAvailable, setTotalMinutesAvailable] = useState<number>(0);
  // Common fields
  const [callPricePerMinute, setCallPricePerMinute] = useState<number>(0.1);
  const [monthlyAllowance, setMonthlyAllowance] = useState<number>(0);
  const [organizationCreditThreshold, setOrganizationCreditThreshold] = useState<number>(1.0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);

  // Initialize WebSocket connection to the credits namespace
  const { isConnected, on, registerUser } = useWebSocket('credits');

  const fetchCredits = async () => {
    try {
      setIsLoading(true);
      const response = await getUserCredits();

      // Update new monthly credits fields
      setFreeCreditsRemaining(response.freeCreditsRemaining || 0);
      setPaidCredits(response.paidCredits || 0);
      setTotalAvailable(response.totalAvailable || 0);
      setUsingFreeCredits(response.usingFreeCredits || false);
      setFreeMinutesRemaining(response.freeMinutesRemaining || 0);
      setPaidMinutes(response.paidMinutes || 0);
      setTotalMinutesAvailable(response.totalMinutesAvailable || 0);

      // Update legacy fields for backward compatibility
      setCredits(response.credits || response.totalAvailable || 0);
      setMinutes(response.minutes || response.totalMinutesAvailable || 0);
      setCallPricePerMinute(response.callPricePerMinute || 0.1);
      setMonthlyAllowance(response.monthlyAllowance || 0);
      setOrganizationCreditThreshold(response.minimumCreditsThreshold || 1.0);

      return response;
    } catch (error) {
      console.error("Error fetching user credits:", error);
      return {
        credits: 0,
        minutes: 0,
        callPricePerMinute: 0.1,
        freeCreditsRemaining: 0,
        paidCredits: 0,
        totalAvailable: 0,
        usingFreeCredits: false,
        freeMinutesRemaining: 0,
        paidMinutes: 0,
        totalMinutesAvailable: 0,
        monthlyAllowance: 0
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Get user ID and organization ID from localStorage on mount
  useEffect(() => {
    try {
      const userData = localStorage.getItem('user_data');
      if (userData) {
        const parsedData = JSON.parse(userData);
        const userId = parsedData._id || null;
        const organizationId = parsedData.organizationId || null;

        // console.log('Retrieved user data from localStorage:', {
        //   userId,
        //   organizationId,
        //   rawData: parsedData
        // });

        setUserId(userId);
        setOrganizationId(organizationId);

        // If we don't have an organization ID, try to fetch it from the API
        if (!organizationId && userId) {
          console.log('No organization ID found in localStorage, will try to fetch from API');
          fetchCredits().then(() => {
            console.log('Fetched credits and possibly updated organization ID');
          });
        }
      } else {
        console.warn('No user data found in localStorage');
      }
    } catch (error) {
      console.error("Error getting user data from localStorage:", error);
    }
  }, []);

  // Register user with WebSocket when connected and userId is available
  useEffect(() => {
    if (isConnected && userId) {
      console.log('Registering user with WebSocket:', +9*9);
      registerUser(userId);
    }
  }, [isConnected, userId, registerUser]);

  // Set up WebSocket listeners for credit updates
  useEffect(() => {
    if (!isConnected) {
      console.log('WebSocket not connected, skipping credit update listeners setup');
      return;
    }

    console.log('Setting up credit update listeners, organizationId:', organizationId);

    // Listen for credit updates for this user
    const unsubscribeCreditUpdate = on('creditUpdate', (data: { credits: number }) => {
      console.log('Received credit update via WebSocket:', data);
      setCredits(data.credits);
      // Calculate minutes based on current callPricePerMinute
      if (callPricePerMinute > 0) {
        setMinutes(data.credits / callPricePerMinute);
      }
      toast.info(`Your credits have been updated`);
    });

    // Listen for organization credit updates
    const unsubscribeOrgUpdate = on('organizationCreditUpdate', (data: { organizationId: string, credits: number }) => {
      console.log('Received organization credit update via WebSocket:', data);
      console.log('Current organizationId:', organizationId);
      console.log('Received organizationId:', data.organizationId);

      // Only update if it's for our organization
      // Convert both IDs to strings for comparison to avoid type mismatches
      const ourOrgId = organizationId?.toString();
      const receivedOrgId = data.organizationId?.toString();

      console.log('Comparing organization IDs:', {
        ourOrgId,
        receivedOrgId,
        match: ourOrgId === receivedOrgId
      });

      if (ourOrgId && receivedOrgId && ourOrgId === receivedOrgId) {
        console.log('Organization ID matched, updating credits to:', data.credits);
        setCredits(data.credits);
        // Calculate minutes based on current callPricePerMinute
        if (callPricePerMinute > 0) {
          setMinutes(data.credits / callPricePerMinute);
        }
        toast.info(`Your organization credits have been updated`);
      } else {
        console.log('Organization ID did not match or is missing, not updating credits');
        console.log('Our organization ID:', ourOrgId);
        console.log('Received organization ID:', receivedOrgId);
      }
    });

    return () => {
      console.log('Cleaning up credit update listeners');
      unsubscribeCreditUpdate();
      unsubscribeOrgUpdate();
    };
  }, [isConnected, on, organizationId]);

  // Fetch credits on mount and every 30 seconds as a fallback
  useEffect(() => {
    // Initial fetch
    fetchCredits();

    // Set up polling interval (as a fallback)
    const intervalId = setInterval(() => {
      fetchCredits();
    }, 30000); // 30 seconds

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [callPricePerMinute]);

  const refreshCredits = async () => {
    await fetchCredits();
  };

  // Use the higher of the organization threshold or the prop threshold
  const effectiveThreshold = Math.max(creditThreshold, organizationCreditThreshold);
  const hasSufficientCredits = totalAvailable >= effectiveThreshold;

  const value = {
    // Legacy fields for backward compatibility
    credits,
    minutes,
    // New monthly credits fields
    freeCreditsRemaining,
    paidCredits,
    totalAvailable,
    usingFreeCredits,
    freeMinutesRemaining,
    paidMinutes,
    totalMinutesAvailable,
    // Common fields
    callPricePerMinute,
    monthlyAllowance,
    hasSufficientCredits,
    isLoading,
    refreshCredits,
    creditThreshold,
    organizationCreditThreshold,
    effectiveThreshold
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

export function useCredits() {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error("useCredits must be used within a CreditProvider");
  }
  return context;
}
