{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.77.2", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^5.1.0", "date-fns": "^3.6.0", "framer-motion": "^12.4.10", "google-libphonenumber": "^3.2.40", "i18next": "^24.2.2", "lucide-react": "^0.476.0", "moment-timezone": "^0.5.48", "next": "^15.2.4", "next-i18next": "^15.4.2", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-google-recaptcha": "^3.1.0", "react-i18next": "^15.4.1", "react-image-crop": "^11.0.7", "react-phone-input-2": "^2.15.1", "react-timezone-select": "^3.2.8", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/axios": "^0.9.36", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-google-recaptcha": "^2.1.9", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "typescript": "^5"}}