"use client";

import { AgentSidebarProps } from "@/types/agent.types";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { useRouter } from "next/navigation";
import { useAgentsList } from "@/hooks/useAgentList";


export default function AgentSidebar({ currentAgentId, userRole }: AgentSidebarProps) {

    const { agents, agentsisLoading } = useAgentsList();

    const router = useRouter();

    const filteredAgents = agents.filter(agent => 
    userRole === 'superadmin' ? true : agent.status === 'active'
  );

  const handleAgentClick = (agentId: string) => {
    if (agentId !== currentAgentId) {
      router.push(`/agents/edit/${agentId}`); 
    }
  };

  return (
    
    <div className="sticky top-4 w-80 h-[calc(100vh-5rem)] min-h-[calc(100vh-4rem)] border-1 border-border bg-card rounded-lg mt-2">
      <div className="p-4 border-b-2 flex items-center gap-2">
        <h2 className="font-semibold text-lg">Agents</h2>
      </div>
      
      <div className="overflow-y-auto h-[calc(100%-4rem)] scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
        {agentsisLoading ? (
          // Skeleton loading for agents
          Array(5).fill(0).map((_, i) => (
            <div key={i} className="p-3 border-b">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                  <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          ))
        ) : (
        filteredAgents.map((agent) => (
          <div
            key={agent.id}
            onClick={() => handleAgentClick(agent.id)}
            className={`p-3 border-b hover:bg-accent/50 transition-colors cursor-pointer ${
              agent.id === currentAgentId ? "bg-accent" : ""
            }`}
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <Avatar className="h-15 w-15">
                  <AvatarImage src={agent.avatar} />
                  <AvatarFallback>
                    {agent.name.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white dark:border-gray-800 ${
                  agent.status === "active" ? "bg-green-500" : "bg-gray-400"
                }`} />
              </div>
              
              <div className="flex flex-col min-w-0">
                <span className="font-medium truncate text-base">
                  {agent.name}
                </span>
                <span className="text-sm truncate text-gray-700 dark:text-gray-200">
                  {agent.role || 'Assistant'}
                </span>
              </div>
            </div>
          </div>
        )))}
      </div>
    </div>
  );
}