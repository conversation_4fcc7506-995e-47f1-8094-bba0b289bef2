/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

import AgentProfileTab from "@/components/agentscomponents/AgentProfileTab";
import AgentRoleTab from "@/components/agentscomponents/AgentRoleTab";
import AgentActionsTab from "@/components/agentscomponents/AgentActionsTab";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import AgentBrainTab from "@/components/agentscomponents/AgentBrainTab";
import AgentAdvancedTab from "@/components/agentscomponents/AgentAdvancedTab";
import AgentVoiceTab from "@/components/agentscomponents/AgentVoiceTab";
import { useAgent } from "@/hooks/useAgent";

export const agentTabs = [
  { 
    value: 'profile',
    label: 'Profile',
    component: AgentProfileTab
  },
  {
    value: 'prompt',
    label: 'Prompt',
    component: AgentRoleTab
  },
  {
    value: 'voice',
    label: 'Voice',
    component: AgentVoiceTab
  },
  {
    value: 'brain',
    label: 'Brain',
    component: AgentBrainTab
  },
  {
    value: 'actions',
    label: 'Actions',
    component: AgentActionsTab
  },
  {
    value: 'advanced',
    label: 'Advanced',
    component: AgentAdvancedTab
  }
] as const;



export default function CreateAgentContent() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("profile");
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [showError, setShowError] = useState<string | null>(null);

  const {agent, setAgent, phoneNumbers, createAgentMutation } = useAgent();


  const handleCreateChanges = async () => {
    setIsSaving(true);
    setShowError(null);
    try {
      await createAgentMutation.mutateAsync(agent);
      setShowSuccessDialog(true);
    } catch (error) {
      console.error("Error creating agent:", error);
      setShowError(error instanceof Error ? error.message : "Failed to create agent");
    } finally {
      setIsSaving(false);
    }
  };


  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
        <div className="w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"></div>
          <p className="text-lg font-medium">Loading...</p>
      </div>
    );
  }

  return (
    <>
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.push("/agents")}
          className="rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <ArrowLeft className="h-5 w-5" />
          <span className="sr-only">Back</span>
        </Button>
        
        <div>
          <h1 className="text-3xl font-semibold tracking-tight">Create Your New Agent</h1>
          <p className="text-muted-foreground mt-1">
            Configure your AI agent&apos;s capabilities and personality
          </p>
        </div>
      </div>

    {/* Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
        >
            <div className="border-b">
              <TabsList className="w-full justify-start h-auto bg-transparent p-0">
                    {agentTabs.map(tab => (
                      <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-3 bg-transparent"
                      >
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
            </div>

            <div className="bg-card rounded-lg border shadow-sm w-4/5 mx-auto">
                  {agentTabs.map(tab => (
                    <TabsContent 
                      key={tab.value}
                      value={tab.value} 
                      className="m-0 focus-visible:outline-none focus-visible:ring-0"
                    >
                      <tab.component 
                        agent={agent} 
                        setAgent={setAgent} 
                        phoneNumbers={phoneNumbers}
                      />
                    </TabsContent>
                  ))}
                </div>
          </Tabs>
      
      {/* Save Changes Button */}
      <div className="mt-6 flex justify-end">
        <Button 
          onClick={handleCreateChanges}
          disabled={isSaving}
          className="bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600"
        >
          {isSaving ? "Creating..." : "Create Agent"}
        </Button>
      </div>
    </div>

    {/* Success Dialog */}
    <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className="bg-green-100 p-1 rounded-full dark:bg-green-900">
              <svg width="20" height="20" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600 dark:text-green-400">
                <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
              </svg>
            </span>
            Agent Created Successfully
          </DialogTitle>
        </DialogHeader>
        <div className="text-center py-4">
          <p>Your new agent has been created successfully.</p>
        </div>
        <DialogFooter className="sm:justify-center">
          <Button 
            onClick={() => {
              setShowSuccessDialog(false);
              router.push("/agents");
            }}
          >
            Go to Agents
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Error Dialog */}
    {showError && (
      <Dialog open={!!showError} onOpenChange={() => setShowError(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <span className="bg-red-100 p-1 rounded-full dark:bg-red-900">
                <svg width="20" height="20" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-red-600 dark:text-red-400">
                  <path d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM7.49991 7.50019C7.77605 7.50019 8.00009 7.27615 8.00009 7.00001V4.00001C8.00009 3.72387 7.77605 3.49983 7.49991 3.49983C7.22377 3.49983 6.99973 3.72387 6.99973 4.00001V7.00001C6.99973 7.27615 7.22377 7.50019 7.49991 7.50019ZM7.49991 9.00001C7.22377 9.00001 6.99973 9.22405 6.99973 9.50019C6.99973 9.77633 7.22377 10.0004 7.49991 10.0004C7.77605 10.0004 8.00009 9.77633 8.00009 9.50019C8.00009 9.22405 7.77605 9.00001 7.49991 9.00001Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
              </span>
              Error
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-4">
            <p>{showError}</p>
          </div>
          <DialogFooter className="sm:justify-center">
            <Button 
              onClick={() => setShowError(null)}
              variant="outline"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )}
    </>
  );
}